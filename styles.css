* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f5f7fa;
    color: #333;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

h1 {
    color: #1a73e8;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.editor-container {
    display: flex;
    gap: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow: hidden;
}

.toolbox {
    flex: 0 0 300px;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.toolbox-section {
    margin-bottom: 25px;
}

.toolbox-section h3 {
    color: #1a73e8;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.slider-container {
    margin-bottom: 15px;
}

.slider-container label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

.slider-container input[type="range"] {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #1a73e8;
    border-radius: 50%;
    cursor: pointer;
}

.canvas-container {
    flex: 1;
    position: relative;
    background-color: #f0f2f5;
    border-radius: 8px;
    overflow: hidden;
}

#imageCanvas {
    width: 100%;
    height: 100%;
    display: block;
}

.face-highlight {
    position: absolute;
    border: 2px solid #1a73e8;
    border-radius: 50%;
    pointer-events: none;
    display: none;
}

button {
    background-color: #1a73e8;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #1557b0;
}

.upload-section {
    margin-bottom: 20px;
}

.upload-section input[type="file"] {
    display: none;
}

.export-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.export-section select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 1em;
}

@media (max-width: 768px) {
    .editor-container {
        flex-direction: column;
    }

    .toolbox {
        flex: none;
    }

    .canvas-container {
        height: 400px;
    }
}