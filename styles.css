* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f5f7fa;
    color: #333;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

h1 {
    color: #1a73e8;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.editor-container {
    display: flex;
    gap: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow: hidden;
}

.toolbox {
    flex: 0 0 300px;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.toolbox-section {
    margin-bottom: 25px;
}

.toolbox-section h3 {
    color: #1a73e8;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.slider-container {
    margin-bottom: 15px;
}

.slider-container label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

.slider-container input[type="range"] {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: #1a73e8;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider-container input[type="range"]::-webkit-slider-thumb:hover {
    background: #1557b0;
    transform: scale(1.1);
}

.slider-container input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #1a73e8;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.canvas-container {
    flex: 1;
    position: relative;
    background-color: #f0f2f5;
    border-radius: 8px;
    overflow: hidden;
}

#imageCanvas {
    width: 100%;
    height: 500px;
    display: block;
    border: 2px dashed #ddd;
    border-radius: 8px;
    background-color: #fafafa;
}

.face-highlight {
    position: absolute;
    border: 3px solid #1a73e8;
    border-radius: 50%;
    pointer-events: none;
    display: none;
    box-shadow: 0 0 10px rgba(26, 115, 232, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 10px rgba(26, 115, 232, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(26, 115, 232, 0.8);
    }
    100% {
        box-shadow: 0 0 10px rgba(26, 115, 232, 0.5);
    }
}

button {
    background-color: #1a73e8;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #1557b0;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:active {
    transform: translateY(0);
}

.upload-section {
    margin-bottom: 20px;
}

.upload-section input[type="file"] {
    display: none;
}

.export-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.export-section select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 1em;
}

.tool-buttons, .filter-buttons, .selection-tools, .background-tools {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 15px;
}

.tool-buttons button, .filter-buttons button, .selection-tools button, .background-tools button {
    padding: 8px 12px;
    font-size: 0.9em;
}

.layers-panel {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    background-color: white;
}

.layers-list {
    max-height: 150px;
    overflow-y: auto;
    margin-bottom: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 5px;
}

.layer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.layer-item:hover {
    background-color: #e9ecef;
}

.layer-item.active {
    background-color: #1a73e8;
    color: white;
}

.layer-controls {
    display: flex;
    gap: 5px;
}

.layer-controls button {
    flex: 1;
    padding: 6px 8px;
    font-size: 0.8em;
}

.selection-overlay {
    position: absolute;
    border: 2px dashed #1a73e8;
    background-color: rgba(26, 115, 232, 0.1);
    pointer-events: none;
    display: none;
}

.marching-ants {
    border: 2px dashed #1a73e8;
    animation: marchingAnts 1s linear infinite;
}

@keyframes marchingAnts {
    0% { border-offset: 0; }
    100% { border-offset: 8px; }
}

#rotationValue {
    font-weight: bold;
    color: #1a73e8;
    margin-right: 10px;
}

@media (max-width: 768px) {
    .editor-container {
        flex-direction: column;
    }

    .toolbox {
        flex: none;
    }

    .canvas-container {
        height: 400px;
    }

    .tool-buttons, .filter-buttons, .selection-tools, .background-tools {
        grid-template-columns: 1fr;
    }
}