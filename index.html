<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاشرافي - محرر الصور</title>

    <link href="styles.css" rel="stylesheet">
    <script src="script.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>الاشرافي</h1>
            <p>محرر صور ذكي ومتقدم</p>
        </header>

        <main>
            <div class="editor-container">
                <div class="toolbox">
                    <div class="toolbox-section">
                        <h3>تحميل الصورة</h3>
                        <div class="upload-section">
                            <input type="file" id="imageUpload" accept="image/*">
                            <button id="uploadButton">اختر صورة</button>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>تعديلات عامة</h3>
                        <div class="slider-container">
                            <label>السطوع</label>
                            <input type="range" id="brightness" min="0" max="200" value="100">
                        </div>
                        <div class="slider-container">
                            <label>التشبع</label>
                            <input type="range" id="saturation" min="0" max="200" value="100">
                        </div>
                        <div class="slider-container">
                            <label>التباين</label>
                            <input type="range" id="contrast" min="0" max="200" value="100">
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>تعديلات محددة</h3>
                        <button id="detectFace">كشف الوجوه</button>
                        <div class="face-tools" style="display: none;">
                            <div class="slider-container">
                                <label>سطوع الوجه</label>
                                <input type="range" id="faceBrightness" min="0" max="200" value="100">
                            </div>
                            <div class="slider-container">
                                <label>تباين الوجه</label>
                                <input type="range" id="faceContrast" min="0" max="200" value="100">
                            </div>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>تصدير الصورة</h3>
                        <div class="export-section">
                            <select id="exportFormat">
                                <option value="jpg">JPEG</option>
                                <option value="png">PNG</option>
                                <option value="webp">WEBP</option>
                            </select>
                            <button id="exportButton">تصدير الصورة</button>
                        </div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="imageCanvas"></canvas>
                    <div id="faceHighlight" class="face-highlight"></div>
                </div>
            </div>
        </main>
    </div>


</body>
</html>