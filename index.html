<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاشرافي - محرر الصور</title>

    <link href="styles.css" rel="stylesheet">
    <!-- Face-api.js for real face detection -->
    <script src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>الاشرافي</h1>
            <p>محرر صور ذكي ومتقدم</p>
        </header>

        <main>
            <div class="editor-container">
                <div class="toolbox">
                    <div class="toolbox-section">
                        <h3>تحميل الصورة</h3>
                        <div class="upload-section">
                            <input type="file" id="imageUpload" accept="image/*">
                            <button id="uploadButton">اختر صورة</button>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>تعديلات عامة</h3>
                        <div class="slider-container">
                            <label>السطوع</label>
                            <input type="range" id="brightness" min="0" max="200" value="100">
                        </div>
                        <div class="slider-container">
                            <label>التشبع</label>
                            <input type="range" id="saturation" min="0" max="200" value="100">
                        </div>
                        <div class="slider-container">
                            <label>التباين</label>
                            <input type="range" id="contrast" min="0" max="200" value="100">
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>أدوات التحرير</h3>
                        <div class="tool-buttons">
                            <button id="cropTool">قص</button>
                            <button id="rotateTool">تدوير</button>
                            <button id="flipHorizontal">انعكاس أفقي</button>
                            <button id="flipVertical">انعكاس عمودي</button>
                        </div>
                        <div class="slider-container">
                            <label>زاوية التدوير</label>
                            <input type="range" id="rotationAngle" min="-180" max="180" value="0">
                            <span id="rotationValue">0°</span>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>الفلاتر المتقدمة</h3>
                        <div class="filter-buttons">
                            <button id="filterNone">بدون فلتر</button>
                            <button id="filterGrayscale">أبيض وأسود</button>
                            <button id="filterSepia">سيبيا</button>
                            <button id="filterBlur">ضبابية</button>
                            <button id="filterSharpen">حدة</button>
                            <button id="filterVintage">كلاسيكي</button>
                        </div>
                        <div class="slider-container">
                            <label>شدة الفلتر</label>
                            <input type="range" id="filterIntensity" min="0" max="100" value="100">
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>التحديد الذكي</h3>
                        <div class="selection-tools">
                            <button id="smartSelect">تحديد ذكي</button>
                            <button id="magicWand">العصا السحرية</button>
                            <button id="clearSelection">مسح التحديد</button>
                        </div>
                        <div class="slider-container">
                            <label>حساسية التحديد</label>
                            <input type="range" id="selectionTolerance" min="1" max="100" value="30">
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>إزالة الخلفية</h3>
                        <div class="background-tools">
                            <button id="removeBackground">إزالة الخلفية</button>
                            <button id="replaceBackground">استبدال الخلفية</button>
                            <input type="file" id="backgroundUpload" accept="image/*" style="display: none;">
                            <button id="uploadBackground">رفع خلفية جديدة</button>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>الطبقات</h3>
                        <div class="layers-panel">
                            <div id="layersList" class="layers-list"></div>
                            <div class="layer-controls">
                                <button id="addLayer">إضافة طبقة</button>
                                <button id="deleteLayer">حذف طبقة</button>
                                <button id="duplicateLayer">نسخ طبقة</button>
                            </div>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>تعديلات محددة</h3>
                        <button id="detectFace">كشف الوجوه</button>
                        <div class="face-tools" style="display: none;">
                            <div class="slider-container">
                                <label>سطوع الوجه</label>
                                <input type="range" id="faceBrightness" min="0" max="200" value="100">
                            </div>
                            <div class="slider-container">
                                <label>تباين الوجه</label>
                                <input type="range" id="faceContrast" min="0" max="200" value="100">
                            </div>
                        </div>
                    </div>

                    <div class="toolbox-section">
                        <h3>تصدير الصورة</h3>
                        <div class="export-section">
                            <select id="exportFormat">
                                <option value="jpg">JPEG</option>
                                <option value="png">PNG</option>
                                <option value="webp">WEBP</option>
                            </select>
                            <button id="exportButton">تصدير الصورة</button>
                        </div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="imageCanvas"></canvas>
                    <div id="faceHighlight" class="face-highlight"></div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>