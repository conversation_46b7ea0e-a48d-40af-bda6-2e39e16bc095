// Global variables
let canvas, ctx, image = null;
let faceCanvas = document.createElement('canvas');
let faceCtx = faceCanvas.getContext('2d');
let faceDetected = false;
let faceRect = null;

// Advanced editing variables
let layers = [];
let currentLayerIndex = 0;
let rotationAngle = 0;
let currentFilter = 'none';
let filterIntensity = 100;
let isSelecting = false;
let selectionStart = null;
let selectionEnd = null;
let selectedArea = null;
let backgroundRemoved = false;
let originalImageData = null;

// ==================== LAYER MANAGEMENT SYSTEM ====================
class Layer {
    constructor(name, canvas, visible = true, opacity = 1) {
        this.name = name;
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.visible = visible;
        this.opacity = opacity;
        this.id = Date.now() + Math.random();

        if (canvas) {
            this.canvas.width = canvas.width;
            this.canvas.height = canvas.height;
            this.ctx.drawImage(canvas, 0, 0);
        }
    }

    setOpacity(opacity) {
        this.opacity = Math.max(0, Math.min(1, opacity));
    }

    setVisible(visible) {
        this.visible = visible;
    }
}

function createLayer(name, sourceCanvas = null) {
    const layer = new Layer(name, sourceCanvas);
    layers.push(layer);
    updateLayersPanel();
    return layer;
}

function deleteLayer(index) {
    if (layers.length > 1 && index >= 0 && index < layers.length) {
        layers.splice(index, 1);
        if (currentLayerIndex >= layers.length) {
            currentLayerIndex = layers.length - 1;
        }
        updateLayersPanel();
        renderLayers();
    }
}

function duplicateLayer(index) {
    if (index >= 0 && index < layers.length) {
        const originalLayer = layers[index];
        const newLayer = new Layer(originalLayer.name + ' نسخة', originalLayer.canvas);
        layers.splice(index + 1, 0, newLayer);
        updateLayersPanel();
    }
}

function updateLayersPanel() {
    const layersList = document.getElementById('layersList');
    if (!layersList) return;

    layersList.innerHTML = '';
    layers.forEach((layer, index) => {
        const layerItem = document.createElement('div');
        layerItem.className = `layer-item ${index === currentLayerIndex ? 'active' : ''}`;
        layerItem.innerHTML = `
            <span>${layer.name}</span>
            <input type="range" min="0" max="100" value="${layer.opacity * 100}"
                   onchange="setLayerOpacity(${index}, this.value)">
        `;
        layerItem.onclick = () => setCurrentLayer(index);
        layersList.appendChild(layerItem);
    });
}

function setCurrentLayer(index) {
    if (index >= 0 && index < layers.length) {
        currentLayerIndex = index;
        updateLayersPanel();
    }
}

function setLayerOpacity(index, opacity) {
    if (index >= 0 && index < layers.length) {
        layers[index].setOpacity(opacity / 100);
        renderLayers();
    }
}

function renderLayers() {
    if (!canvas || !ctx || layers.length === 0) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    layers.forEach(layer => {
        if (layer.visible) {
            ctx.save();
            ctx.globalAlpha = layer.opacity;
            ctx.drawImage(layer.canvas, 0, 0);
            ctx.restore();
        }
    });
}

// ==================== SMART SELECTION SYSTEM ====================
function getPixelColor(imageData, x, y, width) {
    const index = (y * width + x) * 4;
    return {
        r: imageData.data[index],
        g: imageData.data[index + 1],
        b: imageData.data[index + 2],
        a: imageData.data[index + 3]
    };
}

function colorDistance(color1, color2) {
    const dr = color1.r - color2.r;
    const dg = color1.g - color2.g;
    const db = color1.b - color2.b;
    return Math.sqrt(dr * dr + dg * dg + db * db);
}

function floodFill(imageData, startX, startY, tolerance) {
    const width = imageData.width;
    const height = imageData.height;
    const targetColor = getPixelColor(imageData, startX, startY, width);
    const visited = new Array(width * height).fill(false);
    const selectedPixels = [];

    const stack = [{x: startX, y: startY}];

    while (stack.length > 0) {
        const {x, y} = stack.pop();

        if (x < 0 || x >= width || y < 0 || y >= height) continue;
        if (visited[y * width + x]) continue;

        const currentColor = getPixelColor(imageData, x, y, width);
        const distance = colorDistance(targetColor, currentColor);

        if (distance <= tolerance) {
            visited[y * width + x] = true;
            selectedPixels.push({x, y});

            stack.push({x: x + 1, y});
            stack.push({x: x - 1, y});
            stack.push({x, y: y + 1});
            stack.push({x, y: y - 1});
        }
    }

    return selectedPixels;
}

function createSelectionMask(selectedPixels, width, height) {
    const maskCanvas = document.createElement('canvas');
    maskCanvas.width = width;
    maskCanvas.height = height;
    const maskCtx = maskCanvas.getContext('2d');

    const imageData = maskCtx.createImageData(width, height);

    selectedPixels.forEach(pixel => {
        const index = (pixel.y * width + pixel.x) * 4;
        imageData.data[index] = 255;     // R
        imageData.data[index + 1] = 255; // G
        imageData.data[index + 2] = 255; // B
        imageData.data[index + 3] = 255; // A
    });

    maskCtx.putImageData(imageData, 0, 0);
    return maskCanvas;
}

function smartSelect(x, y) {
    if (!canvas || !ctx) return;

    const tolerance = document.getElementById('selectionTolerance')?.value || 30;
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const selectedPixels = floodFill(imageData, x, y, tolerance);

    if (selectedPixels.length > 0) {
        selectedArea = createSelectionMask(selectedPixels, canvas.width, canvas.height);
        showSelection();
    }
}

function showSelection() {
    if (!selectedArea) return;

    // Create selection overlay
    let overlay = document.getElementById('selectionOverlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'selectionOverlay';
        overlay.className = 'selection-overlay marching-ants';
        document.querySelector('.canvas-container').appendChild(overlay);
    }

    overlay.style.display = 'block';
    overlay.style.left = '0px';
    overlay.style.top = '0px';
    overlay.style.width = canvas.width + 'px';
    overlay.style.height = canvas.height + 'px';
}

function clearSelection() {
    selectedArea = null;
    const overlay = document.getElementById('selectionOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// ==================== BACKGROUND REMOVAL SYSTEM ====================
function removeBackground() {
    if (!canvas || !ctx || !image) return;

    // Store original image data
    if (!originalImageData) {
        originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    }

    // Simple background removal using edge detection and color analysis
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Detect edges and likely background areas
    const backgroundMask = detectBackground(imageData);

    // Apply transparency to background areas
    for (let i = 0; i < data.length; i += 4) {
        const pixelIndex = i / 4;
        if (backgroundMask[pixelIndex]) {
            data[i + 3] = 0; // Set alpha to 0 (transparent)
        }
    }

    ctx.putImageData(imageData, 0, 0);
    backgroundRemoved = true;
}

function detectBackground(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const backgroundMask = new Array(width * height).fill(false);

    // Sample edge pixels to determine background color
    const edgeColors = [];

    // Sample top and bottom edges
    for (let x = 0; x < width; x += 10) {
        edgeColors.push(getPixelColor(imageData, x, 0, width));
        edgeColors.push(getPixelColor(imageData, x, height - 1, width));
    }

    // Sample left and right edges
    for (let y = 0; y < height; y += 10) {
        edgeColors.push(getPixelColor(imageData, 0, y, width));
        edgeColors.push(getPixelColor(imageData, width - 1, y, width));
    }

    // Find most common edge color (likely background)
    const backgroundColor = findDominantColor(edgeColors);
    const tolerance = 50;

    // Mark pixels similar to background color
    for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
            const pixelColor = getPixelColor(imageData, x, y, width);
            const distance = colorDistance(backgroundColor, pixelColor);

            if (distance <= tolerance) {
                backgroundMask[y * width + x] = true;
            }
        }
    }

    return backgroundMask;
}

function findDominantColor(colors) {
    if (colors.length === 0) return {r: 255, g: 255, b: 255, a: 255};

    // Simple approach: return the first color (can be improved)
    return colors[0];
}

function replaceBackground(newBackgroundImage) {
    if (!canvas || !ctx || !backgroundRemoved) {
        alert('يرجى إزالة الخلفية أولاً');
        return;
    }

    // Draw new background
    ctx.save();
    ctx.globalCompositeOperation = 'destination-over';

    // Scale background to fit canvas
    const aspectRatio = newBackgroundImage.width / newBackgroundImage.height;
    const canvasRatio = canvas.width / canvas.height;

    let bgWidth, bgHeight, bgX, bgY;
    if (aspectRatio > canvasRatio) {
        bgHeight = canvas.height;
        bgWidth = canvas.height * aspectRatio;
        bgX = (canvas.width - bgWidth) / 2;
        bgY = 0;
    } else {
        bgWidth = canvas.width;
        bgHeight = canvas.width / aspectRatio;
        bgX = 0;
        bgY = (canvas.height - bgHeight) / 2;
    }

    ctx.drawImage(newBackgroundImage, bgX, bgY, bgWidth, bgHeight);
    ctx.restore();
}

// ==================== ADVANCED FILTERS SYSTEM ====================
function applyFilter(filterType, intensity = 100) {
    if (!canvas || !ctx || !image) return;

    currentFilter = filterType;
    filterIntensity = intensity;

    // Redraw image with current adjustments
    applyAdjustments();

    // Apply the selected filter
    switch (filterType) {
        case 'none':
            // No additional filter
            break;
        case 'grayscale':
            applyGrayscaleFilter(intensity);
            break;
        case 'sepia':
            applySepiaFilter(intensity);
            break;
        case 'blur':
            applyBlurFilter(intensity);
            break;
        case 'sharpen':
            applySharpenFilter(intensity);
            break;
        case 'vintage':
            applyVintageFilter(intensity);
            break;
    }
}

function applyGrayscaleFilter(intensity) {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
        const factor = intensity / 100;

        data[i] = data[i] * (1 - factor) + gray * factor;     // R
        data[i + 1] = data[i + 1] * (1 - factor) + gray * factor; // G
        data[i + 2] = data[i + 2] * (1 - factor) + gray * factor; // B
    }

    ctx.putImageData(imageData, 0, 0);
}

function applySepiaFilter(intensity) {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        const sepiaR = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));
        const sepiaG = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168));
        const sepiaB = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131));

        const factor = intensity / 100;

        data[i] = r * (1 - factor) + sepiaR * factor;
        data[i + 1] = g * (1 - factor) + sepiaG * factor;
        data[i + 2] = b * (1 - factor) + sepiaB * factor;
    }

    ctx.putImageData(imageData, 0, 0);
}

function applyBlurFilter(intensity) {
    const blurAmount = (intensity / 100) * 10;
    ctx.filter = `blur(${blurAmount}px)`;
    drawImage();
    ctx.filter = 'none';
}

function applySharpenFilter(intensity) {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;
    const factor = intensity / 100;

    // Sharpening kernel
    const kernel = [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0
    ];

    const newData = new Uint8ClampedArray(data);

    for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
            for (let c = 0; c < 3; c++) { // RGB channels
                let sum = 0;
                for (let ky = -1; ky <= 1; ky++) {
                    for (let kx = -1; kx <= 1; kx++) {
                        const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                        sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)];
                    }
                }
                const idx = (y * width + x) * 4 + c;
                newData[idx] = data[idx] * (1 - factor) + Math.max(0, Math.min(255, sum)) * factor;
            }
        }
    }

    const newImageData = new ImageData(newData, width, height);
    ctx.putImageData(newImageData, 0, 0);
}

function applyVintageFilter(intensity) {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const factor = intensity / 100;

    for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        // Vintage effect: reduce blue, enhance red/yellow
        const vintageR = Math.min(255, r * 1.2);
        const vintageG = Math.min(255, g * 1.1);
        const vintageB = Math.max(0, b * 0.8);

        data[i] = r * (1 - factor) + vintageR * factor;
        data[i + 1] = g * (1 - factor) + vintageG * factor;
        data[i + 2] = b * (1 - factor) + vintageB * factor;
    }

    ctx.putImageData(imageData, 0, 0);
}

// ==================== TRANSFORMATION SYSTEM ====================
function rotateImage(angle) {
    if (!canvas || !ctx || !image) return;

    rotationAngle = angle;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Save context
    ctx.save();

    // Move to center, rotate, then move back
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((angle * Math.PI) / 180);
    ctx.translate(-canvas.width / 2, -canvas.height / 2);

    // Draw image
    drawImage();

    // Restore context
    ctx.restore();

    // Update rotation value display
    const rotationValue = document.getElementById('rotationValue');
    if (rotationValue) {
        rotationValue.textContent = angle + '°';
    }
}

function flipHorizontal() {
    if (!canvas || !ctx || !image) return;

    ctx.save();
    ctx.scale(-1, 1);
    ctx.translate(-canvas.width, 0);
    drawImage();
    ctx.restore();
}

function flipVertical() {
    if (!canvas || !ctx || !image) return;

    ctx.save();
    ctx.scale(1, -1);
    ctx.translate(0, -canvas.height);
    drawImage();
    ctx.restore();
}

function cropImage() {
    if (!selectedArea || !canvas || !ctx) {
        alert('يرجى تحديد منطقة للقص أولاً');
        return;
    }

    // Create new canvas with cropped area
    const croppedCanvas = document.createElement('canvas');
    const croppedCtx = croppedCanvas.getContext('2d');

    // Set canvas size to selection size
    croppedCanvas.width = selectedArea.width;
    croppedCanvas.height = selectedArea.height;

    // Draw cropped area
    croppedCtx.drawImage(selectedArea, 0, 0);

    // Replace current image with cropped version
    const newImage = new Image();
    newImage.onload = () => {
        image = newImage;
        drawImage();
        clearSelection();
    };
    newImage.src = croppedCanvas.toDataURL();
}

// Layer class
class Layer {
    constructor(name, canvas, visible = true, opacity = 1) {
        this.name = name;
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.visible = visible;
        this.opacity = opacity;
        this.id = Date.now() + Math.random();

        if (canvas) {
            this.canvas.width = canvas.width;
            this.canvas.height = canvas.height;
            this.ctx.drawImage(canvas, 0, 0);
        }
    }

    setOpacity(opacity) {
        this.opacity = Math.max(0, Math.min(1, opacity));
    }

    setVisible(visible) {
        this.visible = visible;
    }
}

// Initialize canvas size
function initCanvas() {
    if (!canvas) {
        canvas = document.getElementById('imageCanvas');
        ctx = canvas.getContext('2d');
    }
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
}

// Setup image upload
function setupImageUpload() {
    const uploadButton = document.getElementById('uploadButton');
    const imageUpload = document.getElementById('imageUpload');

    if (uploadButton && imageUpload) {
        uploadButton.addEventListener('click', () => {
            imageUpload.click();
        });

        imageUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    image = new Image();
                    image.onload = () => {
                        // Initialize layers system
                        layers = [];
                        currentLayerIndex = 0;

                        // Create base layer
                        const baseLayer = createLayer('الطبقة الأساسية');
                        baseLayer.ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

                        drawImage();
                        resetSliders();
                        renderLayers();
                    };
                    image.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Draw image on canvas
function drawImage() {
    if (!image) return;

    // Reset canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate aspect ratio
    const aspectRatio = image.width / image.height;
    const canvasRatio = canvas.width / canvas.height;

    let width, height;
    if (aspectRatio > canvasRatio) {
        width = canvas.width;
        height = canvas.width / aspectRatio;
    } else {
        height = canvas.height;
        width = canvas.height * aspectRatio;
    }

    // Center image
    const x = (canvas.width - width) / 2;
    const y = (canvas.height - height) / 2;

    ctx.drawImage(image, x, y, width, height);
}

// Reset sliders to default values
function resetSliders() {
    const sliders = ['brightness', 'saturation', 'contrast', 'faceBrightness', 'faceContrast'];
    sliders.forEach(sliderId => {
        const slider = document.getElementById(sliderId);
        if (slider) {
            slider.value = 100;
        }
    });

    // Hide face tools when resetting
    const faceTools = document.querySelector('.face-tools');
    const faceHighlight = document.getElementById('faceHighlight');
    if (faceTools) faceTools.style.display = 'none';
    if (faceHighlight) faceHighlight.style.display = 'none';

    faceDetected = false;
    faceRect = null;
}

// Apply general adjustments
function applyAdjustments() {
    if (!image || !canvas || !ctx) return;

    // Get slider values
    const brightnessSlider = document.getElementById('brightness');
    const saturationSlider = document.getElementById('saturation');
    const contrastSlider = document.getElementById('contrast');

    if (!brightnessSlider || !saturationSlider || !contrastSlider) return;

    const brightness = brightnessSlider.value / 100;
    const saturation = saturationSlider.value / 100;
    const contrast = contrastSlider.value / 100;

    // Apply adjustments
    ctx.filter = `brightness(${brightness}) saturate(${saturation}) contrast(${contrast})`;
    drawImage();

    // Reset filter for other operations
    ctx.filter = 'none';
}

// Apply face adjustments
function applyFaceAdjustments() {
    if (!faceDetected || !faceRect || !image || !canvas) return;

    // Get slider values
    const faceBrightnessSlider = document.getElementById('faceBrightness');
    const faceContrastSlider = document.getElementById('faceContrast');

    if (!faceBrightnessSlider || !faceContrastSlider) return;

    const faceBrightness = faceBrightnessSlider.value / 100;
    const faceContrast = faceContrastSlider.value / 100;

    // First redraw the original image
    applyAdjustments();

    // Then apply face-specific adjustments to the detected area
    ctx.save();
    ctx.filter = `brightness(${faceBrightness}) contrast(${faceContrast})`;

    // Create a clipping path for the face area
    ctx.beginPath();
    ctx.ellipse(faceRect.x + faceRect.width/2, faceRect.y + faceRect.height/2,
                faceRect.width/2, faceRect.height/2, 0, 0, 2 * Math.PI);
    ctx.clip();

    // Redraw the face area with the new filter
    const aspectRatio = image.width / image.height;
    const canvasRatio = canvas.width / canvas.height;
    let width, height, x, y;

    if (aspectRatio > canvasRatio) {
        width = canvas.width;
        height = canvas.width / aspectRatio;
    } else {
        height = canvas.height;
        width = canvas.height * aspectRatio;
    }

    x = (canvas.width - width) / 2;
    y = (canvas.height - height) / 2;

    ctx.drawImage(image, x, y, width, height);
    ctx.restore();
}

// Detect faces
function setupFaceDetection() {
    const detectButton = document.getElementById('detectFace');
    if (detectButton) {
        detectButton.addEventListener('click', async () => {
            if (!image || !canvas) return;

            // Simulate face detection (in real application, use face detection library)
            const faceWidth = Math.min(image.width * 0.3, canvas.width * 0.3);
            const faceHeight = faceWidth * 0.8;
            const faceX = (canvas.width - faceWidth) / 2;
            const faceY = (canvas.height - faceHeight) / 2;

            faceRect = { x: faceX, y: faceY, width: faceWidth, height: faceHeight };

            // Show face highlight
            const faceHighlight = document.getElementById('faceHighlight');
            if (faceHighlight) {
                faceHighlight.style.display = 'block';
                faceHighlight.style.width = `${faceWidth}px`;
                faceHighlight.style.height = `${faceHeight}px`;
                faceHighlight.style.left = `${faceX}px`;
                faceHighlight.style.top = `${faceY}px`;
            }

            // Show face tools
            const faceTools = document.querySelector('.face-tools');
            if (faceTools) {
                faceTools.style.display = 'block';
            }

            faceDetected = true;
        });
    }
}

// Setup export functionality
function setupExport() {
    const exportButton = document.getElementById('exportButton');
    if (exportButton) {
        exportButton.addEventListener('click', () => {
            if (!image || !canvas) return;

            const formatSelect = document.getElementById('exportFormat');
            const format = formatSelect ? formatSelect.value : 'png';
            const link = document.createElement('a');
            link.download = `edited_image.${format}`;

            // Get canvas data
            const dataUrl = canvas.toDataURL(`image/${format}`);
            link.href = dataUrl;

            // Trigger download
            link.click();
        });
    }
}

// ==================== SETUP FUNCTIONS ====================
function setupAdjustmentControls() {
    const controls = [
        { id: 'brightness', handler: applyAdjustments },
        { id: 'saturation', handler: applyAdjustments },
        { id: 'contrast', handler: applyAdjustments },
        { id: 'faceBrightness', handler: applyFaceAdjustments },
        { id: 'faceContrast', handler: applyFaceAdjustments }
    ];

    controls.forEach(control => {
        const element = document.getElementById(control.id);
        if (element) {
            element.addEventListener('input', control.handler);
        }
    });
}

function setupEditingTools() {
    // Crop tool
    const cropTool = document.getElementById('cropTool');
    if (cropTool) {
        cropTool.addEventListener('click', cropImage);
    }

    // Rotation
    const rotationSlider = document.getElementById('rotationAngle');
    if (rotationSlider) {
        rotationSlider.addEventListener('input', (e) => {
            rotateImage(parseInt(e.target.value));
        });
    }

    // Flip tools
    const flipH = document.getElementById('flipHorizontal');
    const flipV = document.getElementById('flipVertical');
    if (flipH) flipH.addEventListener('click', flipHorizontal);
    if (flipV) flipV.addEventListener('click', flipVertical);
}

function setupFilterControls() {
    const filterButtons = [
        { id: 'filterNone', filter: 'none' },
        { id: 'filterGrayscale', filter: 'grayscale' },
        { id: 'filterSepia', filter: 'sepia' },
        { id: 'filterBlur', filter: 'blur' },
        { id: 'filterSharpen', filter: 'sharpen' },
        { id: 'filterVintage', filter: 'vintage' }
    ];

    filterButtons.forEach(btn => {
        const element = document.getElementById(btn.id);
        if (element) {
            element.addEventListener('click', () => {
                const intensity = document.getElementById('filterIntensity')?.value || 100;
                applyFilter(btn.filter, intensity);
            });
        }
    });

    const intensitySlider = document.getElementById('filterIntensity');
    if (intensitySlider) {
        intensitySlider.addEventListener('input', (e) => {
            applyFilter(currentFilter, parseInt(e.target.value));
        });
    }
}

function setupSelectionTools() {
    // Smart selection
    const smartSelectBtn = document.getElementById('smartSelect');
    if (smartSelectBtn) {
        smartSelectBtn.addEventListener('click', () => {
            isSelecting = true;
            canvas.style.cursor = 'crosshair';
        });
    }

    // Magic wand
    const magicWandBtn = document.getElementById('magicWand');
    if (magicWandBtn) {
        magicWandBtn.addEventListener('click', () => {
            isSelecting = true;
            canvas.style.cursor = 'crosshair';
        });
    }

    // Clear selection
    const clearBtn = document.getElementById('clearSelection');
    if (clearBtn) {
        clearBtn.addEventListener('click', clearSelection);
    }

    // Canvas click for selection
    if (canvas) {
        canvas.addEventListener('click', (e) => {
            if (isSelecting) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                smartSelect(x, y);
                isSelecting = false;
                canvas.style.cursor = 'default';
            }
        });
    }
}

function setupBackgroundTools() {
    // Remove background
    const removeBtn = document.getElementById('removeBackground');
    if (removeBtn) {
        removeBtn.addEventListener('click', removeBackground);
    }

    // Replace background
    const replaceBtn = document.getElementById('replaceBackground');
    if (replaceBtn) {
        replaceBtn.addEventListener('click', () => {
            document.getElementById('backgroundUpload')?.click();
        });
    }

    // Background upload
    const uploadBtn = document.getElementById('uploadBackground');
    const uploadInput = document.getElementById('backgroundUpload');
    if (uploadBtn && uploadInput) {
        uploadBtn.addEventListener('click', () => {
            uploadInput.click();
        });

        uploadInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const bgImage = new Image();
                    bgImage.onload = () => {
                        replaceBackground(bgImage);
                    };
                    bgImage.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

function setupLayerControls() {
    // Add layer
    const addBtn = document.getElementById('addLayer');
    if (addBtn) {
        addBtn.addEventListener('click', () => {
            createLayer('طبقة جديدة ' + (layers.length + 1));
        });
    }

    // Delete layer
    const deleteBtn = document.getElementById('deleteLayer');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', () => {
            deleteLayer(currentLayerIndex);
        });
    }

    // Duplicate layer
    const duplicateBtn = document.getElementById('duplicateLayer');
    if (duplicateBtn) {
        duplicateBtn.addEventListener('click', () => {
            duplicateLayer(currentLayerIndex);
        });
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCanvas();
    setupImageUpload();
    setupFaceDetection();
    setupExport();
    setupAdjustmentControls();
    setupEditingTools();
    setupFilterControls();
    setupSelectionTools();
    setupBackgroundTools();
    setupLayerControls();

    // Initialize canvas size on window resize
    window.addEventListener('resize', initCanvas);
});