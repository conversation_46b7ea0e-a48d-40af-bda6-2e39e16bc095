// Global variables
let canvas = document.getElementById('imageCanvas');
let ctx = canvas.getContext('2d');
let image = null;
let faceCanvas = document.createElement('canvas');
let faceCtx = faceCanvas.getContext('2d');
let faceDetected = false;
let faceRect = null;

// Initialize canvas size
function initCanvas() {
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
}

// Load image
document.getElementById('uploadButton').addEventListener('click', () => {
    document.getElementById('imageUpload').click();
});

document.getElementById('imageUpload').addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
            image = new Image();
            image.onload = () => {
                drawImage();
                resetSliders();
            };
            image.src = event.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// Draw image on canvas
function drawImage() {
    if (!image) return;

    // Reset canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate aspect ratio
    const aspectRatio = image.width / image.height;
    const canvasRatio = canvas.width / canvas.height;

    let width, height;
    if (aspectRatio > canvasRatio) {
        width = canvas.width;
        height = canvas.width / aspectRatio;
    } else {
        height = canvas.height;
        width = canvas.height * aspectRatio;
    }

    // Center image
    const x = (canvas.width - width) / 2;
    const y = (canvas.height - height) / 2;

    ctx.drawImage(image, x, y, width, height);
}

// Reset sliders to default values
function resetSliders() {
    document.getElementById('brightness').value = 100;
    document.getElementById('saturation').value = 100;
    document.getElementById('contrast').value = 100;
    document.getElementById('faceBrightness').value = 100;
    document.getElementById('faceContrast').value = 100;
}

// Apply general adjustments
function applyAdjustments() {
    if (!image) return;

    // Get slider values
    const brightness = document.getElementById('brightness').value / 100;
    const saturation = document.getElementById('saturation').value / 100;
    const contrast = document.getElementById('contrast').value / 100;

    // Apply adjustments
    ctx.filter = `brightness(${brightness}) saturate(${saturation}) contrast(${contrast})`;
    drawImage();
}

// Apply face adjustments
function applyFaceAdjustments() {
    if (!faceDetected || !faceRect) return;

    // Get slider values
    const faceBrightness = document.getElementById('faceBrightness').value / 100;
    const faceContrast = document.getElementById('faceContrast').value / 100;

    // Apply adjustments
    faceCtx.filter = `brightness(${faceBrightness}) contrast(${faceContrast})`;
    faceCtx.drawImage(image, faceRect.x, faceRect.y, faceRect.width, faceRect.height,
                     faceRect.x, faceRect.y, faceRect.width, faceRect.height);
}

// Detect faces
document.getElementById('detectFace').addEventListener('click', async () => {
    if (!image) return;

    // Simulate face detection (in real application, use face detection library)
    const faceWidth = Math.min(image.width * 0.3, canvas.width * 0.3);
    const faceHeight = faceWidth * 0.8;
    const faceX = (canvas.width - faceWidth) / 2;
    const faceY = (canvas.height - faceHeight) / 2;

    faceRect = { x: faceX, y: faceY, width: faceWidth, height: faceHeight };

    // Show face highlight
    const faceHighlight = document.getElementById('faceHighlight');
    faceHighlight.style.display = 'block';
    faceHighlight.style.width = `${faceWidth}px`;
    faceHighlight.style.height = `${faceHeight}px`;
    faceHighlight.style.left = `${faceX}px`;
    faceHighlight.style.top = `${faceY}px`;

    // Show face tools
    document.querySelector('.face-tools').style.display = 'block';

    faceDetected = true;
});

// Export image
document.getElementById('exportButton').addEventListener('click', () => {
    if (!image) return;

    const format = document.getElementById('exportFormat').value;
    const link = document.createElement('a');
    link.download = `edited_image.${format}`;

    // Get canvas data
    const dataUrl = canvas.toDataURL(`image/${format}`);
    link.href = dataUrl;

    // Trigger download
    link.click();
});

// Event listeners for adjustments
document.getElementById('brightness').addEventListener('input', applyAdjustments);
document.getElementById('saturation').addEventListener('input', applyAdjustments);
document.getElementById('contrast').addEventListener('input', applyAdjustments);
document.getElementById('faceBrightness').addEventListener('input', applyFaceAdjustments);
document.getElementById('faceContrast').addEventListener('input', applyFaceAdjustments);

// Initialize canvas size on window resize
window.addEventListener('resize', initCanvas);

// Initialize application
initCanvas();