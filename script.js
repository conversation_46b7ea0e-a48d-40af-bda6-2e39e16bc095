// Global variables
let canvas, ctx, image = null;
let faceCanvas = document.createElement('canvas');
let faceCtx = faceCanvas.getContext('2d');
let faceDetected = false;
let faceRect = null;

// Initialize canvas size
function initCanvas() {
    if (!canvas) {
        canvas = document.getElementById('imageCanvas');
        ctx = canvas.getContext('2d');
    }
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
}

// Setup image upload
function setupImageUpload() {
    const uploadButton = document.getElementById('uploadButton');
    const imageUpload = document.getElementById('imageUpload');

    if (uploadButton && imageUpload) {
        uploadButton.addEventListener('click', () => {
            imageUpload.click();
        });

        imageUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    image = new Image();
                    image.onload = () => {
                        drawImage();
                        resetSliders();
                    };
                    image.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Draw image on canvas
function drawImage() {
    if (!image) return;

    // Reset canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Calculate aspect ratio
    const aspectRatio = image.width / image.height;
    const canvasRatio = canvas.width / canvas.height;

    let width, height;
    if (aspectRatio > canvasRatio) {
        width = canvas.width;
        height = canvas.width / aspectRatio;
    } else {
        height = canvas.height;
        width = canvas.height * aspectRatio;
    }

    // Center image
    const x = (canvas.width - width) / 2;
    const y = (canvas.height - height) / 2;

    ctx.drawImage(image, x, y, width, height);
}

// Reset sliders to default values
function resetSliders() {
    const sliders = ['brightness', 'saturation', 'contrast', 'faceBrightness', 'faceContrast'];
    sliders.forEach(sliderId => {
        const slider = document.getElementById(sliderId);
        if (slider) {
            slider.value = 100;
        }
    });

    // Hide face tools when resetting
    const faceTools = document.querySelector('.face-tools');
    const faceHighlight = document.getElementById('faceHighlight');
    if (faceTools) faceTools.style.display = 'none';
    if (faceHighlight) faceHighlight.style.display = 'none';

    faceDetected = false;
    faceRect = null;
}

// Apply general adjustments
function applyAdjustments() {
    if (!image || !canvas || !ctx) return;

    // Get slider values
    const brightnessSlider = document.getElementById('brightness');
    const saturationSlider = document.getElementById('saturation');
    const contrastSlider = document.getElementById('contrast');

    if (!brightnessSlider || !saturationSlider || !contrastSlider) return;

    const brightness = brightnessSlider.value / 100;
    const saturation = saturationSlider.value / 100;
    const contrast = contrastSlider.value / 100;

    // Apply adjustments
    ctx.filter = `brightness(${brightness}) saturate(${saturation}) contrast(${contrast})`;
    drawImage();

    // Reset filter for other operations
    ctx.filter = 'none';
}

// Apply face adjustments
function applyFaceAdjustments() {
    if (!faceDetected || !faceRect || !image || !canvas) return;

    // Get slider values
    const faceBrightnessSlider = document.getElementById('faceBrightness');
    const faceContrastSlider = document.getElementById('faceContrast');

    if (!faceBrightnessSlider || !faceContrastSlider) return;

    const faceBrightness = faceBrightnessSlider.value / 100;
    const faceContrast = faceContrastSlider.value / 100;

    // First redraw the original image
    applyAdjustments();

    // Then apply face-specific adjustments to the detected area
    ctx.save();
    ctx.filter = `brightness(${faceBrightness}) contrast(${faceContrast})`;

    // Create a clipping path for the face area
    ctx.beginPath();
    ctx.ellipse(faceRect.x + faceRect.width/2, faceRect.y + faceRect.height/2,
                faceRect.width/2, faceRect.height/2, 0, 0, 2 * Math.PI);
    ctx.clip();

    // Redraw the face area with the new filter
    const aspectRatio = image.width / image.height;
    const canvasRatio = canvas.width / canvas.height;
    let width, height, x, y;

    if (aspectRatio > canvasRatio) {
        width = canvas.width;
        height = canvas.width / aspectRatio;
    } else {
        height = canvas.height;
        width = canvas.height * aspectRatio;
    }

    x = (canvas.width - width) / 2;
    y = (canvas.height - height) / 2;

    ctx.drawImage(image, x, y, width, height);
    ctx.restore();
}

// Detect faces
function setupFaceDetection() {
    const detectButton = document.getElementById('detectFace');
    if (detectButton) {
        detectButton.addEventListener('click', async () => {
            if (!image || !canvas) return;

            // Simulate face detection (in real application, use face detection library)
            const faceWidth = Math.min(image.width * 0.3, canvas.width * 0.3);
            const faceHeight = faceWidth * 0.8;
            const faceX = (canvas.width - faceWidth) / 2;
            const faceY = (canvas.height - faceHeight) / 2;

            faceRect = { x: faceX, y: faceY, width: faceWidth, height: faceHeight };

            // Show face highlight
            const faceHighlight = document.getElementById('faceHighlight');
            if (faceHighlight) {
                faceHighlight.style.display = 'block';
                faceHighlight.style.width = `${faceWidth}px`;
                faceHighlight.style.height = `${faceHeight}px`;
                faceHighlight.style.left = `${faceX}px`;
                faceHighlight.style.top = `${faceY}px`;
            }

            // Show face tools
            const faceTools = document.querySelector('.face-tools');
            if (faceTools) {
                faceTools.style.display = 'block';
            }

            faceDetected = true;
        });
    }
}

// Setup export functionality
function setupExport() {
    const exportButton = document.getElementById('exportButton');
    if (exportButton) {
        exportButton.addEventListener('click', () => {
            if (!image || !canvas) return;

            const formatSelect = document.getElementById('exportFormat');
            const format = formatSelect ? formatSelect.value : 'png';
            const link = document.createElement('a');
            link.download = `edited_image.${format}`;

            // Get canvas data
            const dataUrl = canvas.toDataURL(`image/${format}`);
            link.href = dataUrl;

            // Trigger download
            link.click();
        });
    }
}

// Setup adjustment controls
function setupAdjustmentControls() {
    const controls = [
        { id: 'brightness', handler: applyAdjustments },
        { id: 'saturation', handler: applyAdjustments },
        { id: 'contrast', handler: applyAdjustments },
        { id: 'faceBrightness', handler: applyFaceAdjustments },
        { id: 'faceContrast', handler: applyFaceAdjustments }
    ];

    controls.forEach(control => {
        const element = document.getElementById(control.id);
        if (element) {
            element.addEventListener('input', control.handler);
        }
    });
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCanvas();
    setupImageUpload();
    setupFaceDetection();
    setupExport();
    setupAdjustmentControls();

    // Initialize canvas size on window resize
    window.addEventListener('resize', initCanvas);
});